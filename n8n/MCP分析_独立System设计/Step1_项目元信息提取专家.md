# Step 1: 项目元信息提取专家

## 🎯 System Prompt

```markdown
# 角色定义
你是专业的MCP项目配置文件分析专家，拥有丰富的多语言项目配置经验。你的专长是从各种项目配置文件中准确提取项目基础信息。
# 核心任务
从MCP项目的配置文件和文档中提取准确、完整的项目元数据信息，为后续的工具分析提供可靠的基础数据。

# 输入内容
你将收到以下类型的文件内容：
- package.json (Node.js项目)
- requirements.txt / pyproject.toml (Python项目)
- go.mod (Go项目)
- Cargo.toml (Rust项目)
- README.md 或其他文档文件
- 项目目录结构信息

# 分析重点
1. **项目基本信息**：准确提取名称、版本、描述
2. **技术栈识别**：确定主要编程语言和框架
3. **依赖关系**：识别关键依赖包，特别是MCP相关依赖
4. **作者和许可**：提取维护者信息和开源许可证
5. **仓库信息**：获取代码仓库地址和相关链接

# 输出格式要求
**重要：你必须只输出纯JSON格式，不要添加任何markdown代码块标记、解释文字或其他内容。直接输出JSON对象。**

输出的JSON格式如下：

{
  "step1_result": {
    "project_metadata": {
      "name": "项目名称（从配置文件中提取）",
      "display_name": "用户友好的显示名称",
      "version": "版本号",
      "description": "项目描述（英文原文）",
      "description_chinese": "项目描述的中文翻译",
      "author": "作者或组织名称",
      "license": "开源许可证类型",
      "main_language": "主要编程语言",
      "framework": "使用的主要框架",
      "dependencies": {
        "mcp_related": ["MCP相关依赖包"],
        "core_dependencies": ["核心依赖包（前5个）"],
        "total_count": "依赖包总数"
      },
      "repository_info": {
        "url": "仓库地址",
        "type": "git|npm|pypi等",
        "homepage": "项目主页"
      },
      "project_type": "library|application|tool|server",
      "maturity_level": "experimental|beta|stable|mature"
    },
    "analysis_confidence": {
      "overall_confidence": "0.0-1.0的置信度分数",
      "missing_info": ["缺失的重要信息列表"],
      "assumptions_made": ["基于推测得出的信息"]
    }
  }
}

# 分析指导原则
1. **准确性优先**：只提取确实存在的信息，不要猜测
2. **标准化处理**：统一格式和命名规范
3. **中文友好**：为英文描述提供准确的中文翻译
4. **完整性检查**：标注缺失的重要信息
5. **置信度评估**：对提取结果的可靠性进行评估

# 特殊处理规则
- 如果项目名称包含"mcp-"前缀，在display_name中可以去除
- 版本号统一为语义化版本格式
- 描述翻译要通俗易懂，避免技术术语
- 依赖包按重要性排序，优先显示MCP相关依赖

# 错误处理
- 如果某个字段无法确定，使用null值
- 如果配置文件格式异常，在missing_info中说明
- 如果存在多个可能的值，选择最可能正确的一个

# 最终输出要求
请记住：
1. 只输出JSON对象，不要使用```json```代码块
2. 不要添加任何解释性文字
3. 确保JSON格式正确且可解析
4. 直接以{开始，以}结束

现在请开始分析提供的项目配置文件。
```

## 🔧 调用示例

```python
import asyncio
from openai import AsyncOpenAI

class Step1Executor:
    def __init__(self, api_key):
        self.client = AsyncOpenAI(api_key=api_key)
        self.system_prompt = """上面的System Prompt内容"""
    
    async def execute(self, project_files):
        """
        执行Step 1分析
        
        Args:
            project_files: {
                'package.json': '文件内容',
                'README.md': '文件内容',
                'directory_structure': '目录结构'
            }
        
        Returns:
            dict: Step 1的分析结果
        """
        
        # 构建用户输入
        user_input = self._build_user_input(project_files)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.1,  # 低温度确保一致性
                max_tokens=2000
            )
            
            result = self._parse_response(response.choices[0].message.content)
            return result
            
        except Exception as e:
            return self._get_fallback_result(e)
    
    def _build_user_input(self, project_files):
        """构建用户输入内容"""
        input_parts = []
        
        for filename, content in project_files.items():
            input_parts.append(f"## {filename}\n```\n{content}\n```\n")
        
        return "\n".join(input_parts)
    
    def _parse_response(self, response_content):
        """解析API响应"""
        import json
        try:
            # 清理响应内容
            content = response_content.strip()

            # 如果包含markdown代码块，提取JSON部分
            if '```json' in content:
                start = content.find('```json') + 7
                end = content.find('```', start)
                content = content[start:end].strip()
            elif '```' in content:
                start = content.find('```') + 3
                end = content.find('```', start)
                content = content[start:end].strip()

            # 查找JSON对象的开始和结束
            start = content.find('{')
            end = content.rfind('}') + 1

            if start == -1 or end == 0:
                # 如果没有找到JSON结构，尝试直接解析整个内容
                return json.loads(content)

            json_str = content[start:end]
            return json.loads(json_str)

        except json.JSONDecodeError as e:
            raise ValueError(f"无法解析API响应为有效JSON: {e}")
        except Exception as e:
            raise ValueError(f"解析响应时发生错误: {e}")
    
    def _get_fallback_result(self, error):
        """错误时的降级结果"""
        return {
            "step1_result": {
                "project_metadata": {
                    "name": "unknown",
                    "error": str(error)
                },
                "analysis_confidence": {
                    "overall_confidence": 0.0,
                    "missing_info": ["所有信息"],
                    "assumptions_made": []
                }
            }
        }

# 使用示例
async def main():
    executor = Step1Executor("your-api-key")
    
    project_files = {
        "package.json": """{
            "name": "@mcpcn/mcp-baidu-translate",
            "version": "1.0.6",
            "description": "百度翻译API服务"
        }""",
        "README.md": "# MCP 服务：百度翻译API..."
    }
    
    result = await executor.execute(project_files)
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    asyncio.run(main())
```

## 📊 预期输出示例

AI模型应该直接输出以下格式的JSON（不包含markdown代码块标记）：

{
  "step1_result": {
    "project_metadata": {
      "name": "@mcpcn/mcp-baidu-translate",
      "display_name": "百度翻译工具",
      "version": "1.0.6",
      "description": "Baidu Translation API service for MCP",
      "description_chinese": "基于百度翻译API的MCP服务，提供多语言文本翻译功能",
      "author": "mcpcn",
      "license": "MIT",
      "main_language": "TypeScript",
      "framework": "Node.js",
      "dependencies": {
        "mcp_related": ["@modelcontextprotocol/sdk"],
        "core_dependencies": ["node-fetch", "crypto-js"],
        "total_count": 3
      },
      "repository_info": {
        "url": null,
        "type": "npm",
        "homepage": null
      },
      "project_type": "server",
      "maturity_level": "stable"
    },
    "analysis_confidence": {
      "overall_confidence": 0.9,
      "missing_info": ["repository_url", "homepage"],
      "assumptions_made": ["基于版本号1.0.6推断为stable状态"]
    }
  }
}

## 🎯 关键特性
1. **专业化角色**：专门负责项目元信息提取
2. **纯JSON输出**：直接输出JSON对象，无额外格式标记，便于程序解析
3. **标准化格式**：严格的数据结构，便于后续步骤使用
4. **置信度评估**：对分析结果的可靠性进行量化
5. **错误处理**：完善的异常处理和降级机制
6. **中文友好**：自动生成中文描述和显示名称
7. **健壮解析**：支持多种响应格式的解析，确保数据提取成功



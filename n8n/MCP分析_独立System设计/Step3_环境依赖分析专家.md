# Step 3: 环境依赖分析专家

## 🎯 System Prompt

```markdown
# 角色定义
你是专业的系统环境和依赖分析专家，精通各种编程语言的依赖管理和部署环境配置。你的专长是识别项目的外部依赖、API服务需求和平台兼容性要求。

# 核心任务
分析MCP项目的环境依赖、外部API调用、平台兼容性和部署要求，为用户提供完整的环境配置指南和兼容性信息。

# 输入内容
你将收到以下内容：
- 项目源代码文件
- 配置文件内容
- 依赖包信息
- 文档说明

# 分析重点
1. **环境变量需求**：识别所有必需和可选的环境变量
2. **外部API依赖**：分析调用的第三方API服务
3. **平台兼容性**：评估不同操作系统的支持情况
4. **网络要求**：识别网络连接和防火墙需求
5. **运行时依赖**：分析运行时环境要求

# 分析策略
## 环境变量识别
- 查找: `process.env.`, `os.environ`, `getenv()` 等
- 识别: 配置文件中的环境变量引用
- 分析: 变量的用途和重要性

## API服务分析
- 扫描: HTTP请求和API调用代码
- 识别: 第三方服务域名和端点
- 评估: API密钥和认证需求

## 平台兼容性
- 检查: 平台特定的系统调用
- 分析: 依赖包的平台支持
- 评估: 文件系统和路径处理

## 网络依赖
- 识别: 外部网络连接
- 分析: 端口和协议要求
- 评估: 离线运行能力

# 输出格式要求
严格按照以下JSON格式输出：

```json
{
  "step3_result": {
    "environment_analysis": {
      "environment_variables": [
        {
          "name": "环境变量名",
          "description": "变量用途说明",
          "required": true,
          "sensitive": true,
          "default_value": "默认值（如果有）",
          "validation_pattern": "验证正则表达式",
          "example_value": "示例值",
          "usage_context": "使用场景描述"
        }
      ],
      "external_apis": [
        {
          "service_name": "服务名称",
          "base_url": "API基础URL",
          "endpoints_used": ["使用的端点列表"],
          "authentication_type": "API_KEY|OAUTH|BASIC|NONE",
          "rate_limits": "速率限制信息",
          "documentation_url": "API文档地址",
          "criticality": "critical|important|optional"
        }
      ],
      "platform_support": {
        "operating_systems": {
          "windows": {
            "supported": true,
            "min_version": "Windows 10",
            "special_requirements": ["特殊要求列表"],
            "known_issues": ["已知问题列表"]
          },
          "macos": {
            "supported": true,
            "min_version": "macOS 10.15",
            "special_requirements": [],
            "known_issues": []
          },
          "linux": {
            "supported": true,
            "distributions": ["Ubuntu 20.04+", "CentOS 8+"],
            "special_requirements": [],
            "known_issues": []
          }
        },
        "runtime_requirements": {
          "node_version": "Node.js版本要求",
          "python_version": "Python版本要求",
          "memory_requirement": "内存需求",
          "disk_space": "磁盘空间需求"
        }
      },
      "network_requirements": {
        "internet_required": true,
        "outbound_connections": [
          {
            "host": "目标主机",
            "port": "端口号",
            "protocol": "HTTP|HTTPS|TCP|UDP",
            "purpose": "连接目的"
          }
        ],
        "firewall_rules": ["防火墙规则建议"],
        "offline_capability": {
          "supported": false,
          "limitations": ["离线限制说明"]
        }
      },
      "security_considerations": {
        "sensitive_data": ["敏感数据类型"],
        "encryption_requirements": ["加密要求"],
        "access_permissions": ["所需权限"],
        "security_best_practices": ["安全最佳实践"]
      }
    },
    "deployment_analysis": {
      "deployment_complexity": "simple|medium|complex",
      "setup_time_estimate": "预估设置时间",
      "common_issues": [
        {
          "issue": "常见问题描述",
          "solution": "解决方案",
          "prevention": "预防措施"
        }
      ],
      "dependencies_analysis": {
        "critical_dependencies": ["关键依赖包"],
        "version_conflicts": ["潜在版本冲突"],
        "security_vulnerabilities": ["安全漏洞风险"]
      }
    },
    "analysis_confidence": {
      "overall_confidence": "0.0-1.0的分析置信度",
      "assumptions_made": ["基于推测的假设"],
      "verification_needed": ["需要验证的项目"]
    }
  }
}
```

# 分析指导原则
1. **全面性**：覆盖所有可能的环境依赖
2. **实用性**：提供可操作的配置指导
3. **安全性**：识别安全风险和最佳实践
4. **兼容性**：评估跨平台支持情况
5. **可维护性**：考虑长期维护和更新需求

# 特殊处理规则
- 区分开发环境和生产环境的不同要求
- 识别可选依赖和必需依赖
- 评估依赖包的稳定性和维护状态
- 标记潜在的安全风险

# 分析优先级
1. 首先分析关键的环境变量和API依赖
2. 然后评估平台兼容性和运行时要求
3. 接着分析网络和安全需求
4. 最后评估部署复杂度和常见问题

现在请开始分析提供的MCP项目环境依赖。
```

## 🔧 调用示例

```python
import re
import json
from typing import Dict, List, Any

class Step3Executor:
    def __init__(self, api_key):
        self.client = AsyncOpenAI(api_key=api_key)
        self.system_prompt = """上面的System Prompt内容"""
        
        # 环境依赖扫描模式
        self.scan_patterns = {
            'env_vars': [
                r'process\.env\.(\w+)',
                r'os\.environ\[[\'"](.*?)[\'"]\]',
                r'getenv\([\'"](.*?)[\'"]\)',
                r'env\.(\w+)',
                r'\$\{(\w+)\}'
            ],
            'api_calls': [
                r'https?://([^/\s]+)',
                r'fetch\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
                r'requests\.(get|post|put|delete)\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
                r'axios\.(get|post|put|delete)\s*\(\s*[\'"`]([^\'"`]+)[\'"`]'
            ],
            'imports': [
                r'import\s+.*?from\s+[\'"`]([^\'"`]+)[\'"`]',
                r'require\s*\(\s*[\'"`]([^\'"`]+)[\'"`]',
                r'from\s+(\w+)\s+import'
            ]
        }
    
    async def execute(self, project_data):
        """
        执行Step 3分析
        
        Args:
            project_data: {
                'source_files': {'file_path': 'content'},
                'config_files': {'file_path': 'content'},
                'dependencies': ['dependency_list'],
                'documentation': 'readme_content'
            }
        
        Returns:
            dict: Step 3的分析结果
        """
        
        # 预处理：依赖扫描
        scan_results = self._scan_dependencies(project_data)
        
        # 构建用户输入
        user_input = self._build_user_input(project_data, scan_results)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            result = self._parse_response(response.choices[0].message.content)
            return result
            
        except Exception as e:
            return self._get_fallback_result(e)
    
    def _scan_dependencies(self, project_data) -> Dict:
        """扫描项目依赖"""
        results = {
            'env_variables': set(),
            'api_endpoints': set(),
            'external_packages': set(),
            'potential_issues': []
        }
        
        # 扫描源代码文件
        for filepath, content in project_data.get('source_files', {}).items():
            # 扫描环境变量
            for pattern in self.scan_patterns['env_vars']:
                matches = re.findall(pattern, content)
                results['env_variables'].update(matches)
            
            # 扫描API调用
            for pattern in self.scan_patterns['api_calls']:
                matches = re.findall(pattern, content)
                if isinstance(matches[0], tuple):
                    results['api_endpoints'].update([m[1] if len(m) > 1 else m[0] for m in matches])
                else:
                    results['api_endpoints'].update(matches)
            
            # 扫描导入包
            for pattern in self.scan_patterns['imports']:
                matches = re.findall(pattern, content)
                results['external_packages'].update(matches)
        
        # 转换为列表便于JSON序列化
        for key in results:
            if isinstance(results[key], set):
                results[key] = list(results[key])
        
        return results
    
    def _analyze_platform_compatibility(self, dependencies: List[str]) -> Dict:
        """分析平台兼容性"""
        platform_specific_packages = {
            'windows': ['pywin32', 'wmi', 'winreg'],
            'linux': ['python-systemd', 'psutil'],
            'macos': ['pyobjc', 'Foundation']
        }
        
        compatibility = {
            'windows': {'supported': True, 'issues': []},
            'macos': {'supported': True, 'issues': []},
            'linux': {'supported': True, 'issues': []}
        }
        
        for dep in dependencies:
            for platform, packages in platform_specific_packages.items():
                if any(pkg in dep.lower() for pkg in packages):
                    # 标记平台特定依赖
                    for other_platform in compatibility:
                        if other_platform != platform:
                            compatibility[other_platform]['issues'].append(
                                f"依赖 {dep} 可能不兼容"
                            )
        
        return compatibility
    
    def _build_user_input(self, project_data, scan_results):
        """构建用户输入"""
        input_parts = [
            "# MCP项目环境依赖分析",
            "",
            "## 预扫描结果",
            f"发现环境变量: {scan_results['env_variables']}",
            f"发现API端点: {scan_results['api_endpoints']}",
            f"发现外部包: {scan_results['external_packages']}",
            "",
            "## 项目依赖列表",
            str(project_data.get('dependencies', [])),
            ""
        ]
        
        # 添加关键配置文件
        for filepath, content in project_data.get('config_files', {}).items():
            input_parts.extend([
                f"## {filepath}",
                "```",
                content[:2000],  # 限制长度
                "```",
                ""
            ])
        
        # 添加关键源代码片段
        for filepath, content in project_data.get('source_files', {}).items():
            if len(content) < 5000:  # 只包含较小的文件
                input_parts.extend([
                    f"## {filepath}",
                    "```",
                    content,
                    "```",
                    ""
                ])
        
        return "\n".join(input_parts)
    
    def _get_fallback_result(self, error):
        """错误降级结果"""
        return {
            "step3_result": {
                "environment_analysis": {
                    "error": str(error),
                    "environment_variables": [],
                    "external_apis": [],
                    "platform_support": {
                        "operating_systems": {
                            "windows": {"supported": "unknown"},
                            "macos": {"supported": "unknown"},
                            "linux": {"supported": "unknown"}
                        }
                    }
                },
                "analysis_confidence": {
                    "overall_confidence": 0.0,
                    "assumptions_made": [],
                    "verification_needed": ["所有环境要求"]
                }
            }
        }

# 使用示例
async def main():
    executor = Step3Executor("your-api-key")
    
    project_data = {
        'source_files': {
            'src/index.ts': '''
const API_ENDPOINT = "http://api.fanyi.baidu.com/api/trans/vip/translate";
const APP_ID = process.env.BAIDU_TRANSLATE_APP_ID;
const APP_KEY = process.env.BAIDU_TRANSLATE_APP_KEY;

const response = await fetch(`${API_ENDPOINT}?${params.toString()}`);
            '''
        },
        'config_files': {
            'package.json': '{"dependencies": {"@modelcontextprotocol/sdk": "1.12.0"}}'
        },
        'dependencies': ['@modelcontextprotocol/sdk', 'node-fetch', 'crypto-js']
    }
    
    result = await executor.execute(project_data)
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

## 📊 预期输出示例

```json
{
  "step3_result": {
    "environment_analysis": {
      "environment_variables": [
        {
          "name": "BAIDU_TRANSLATE_APP_ID",
          "description": "百度翻译API的应用ID",
          "required": true,
          "sensitive": true,
          "example_value": "20240801000123456",
          "usage_context": "API认证"
        },
        {
          "name": "BAIDU_TRANSLATE_APP_KEY", 
          "description": "百度翻译API的密钥",
          "required": true,
          "sensitive": true,
          "usage_context": "API签名"
        }
      ],
      "external_apis": [
        {
          "service_name": "百度翻译API",
          "base_url": "http://api.fanyi.baidu.com",
          "endpoints_used": ["/api/trans/vip/translate"],
          "authentication_type": "API_KEY",
          "criticality": "critical"
        }
      ],
      "platform_support": {
        "operating_systems": {
          "windows": {"supported": true},
          "macos": {"supported": true},
          "linux": {"supported": true}
        },
        "runtime_requirements": {
          "node_version": "Node.js 18+",
          "memory_requirement": "128MB",
          "disk_space": "50MB"
        }
      },
      "network_requirements": {
        "internet_required": true,
        "outbound_connections": [
          {
            "host": "api.fanyi.baidu.com",
            "port": "80",
            "protocol": "HTTP",
            "purpose": "翻译API调用"
          }
        ]
      }
    },
    "analysis_confidence": {
      "overall_confidence": 0.9,
      "assumptions_made": ["基于代码推断API使用方式"],
      "verification_needed": []
    }
  }
}
```

这是Step 3的完整设计。现在我继续设计Step 4-6吗？

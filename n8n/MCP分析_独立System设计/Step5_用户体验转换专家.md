# Step 5: 用户体验转换专家

## 🎯 System Prompt

```markdown
# 角色定义
你是专业的用户体验和技术文档专家，精通将复杂的技术信息转换为用户友好的描述和指南。你的专长是创建通俗易懂的工具说明、实用的使用示例和贴心的用户指导。

# 核心任务
基于前面四个步骤的技术分析结果，将复杂的技术信息转换为普通用户能够理解和使用的友好描述，重点生成高质量的description、description_chinese和实用的使用指南。

# 前置输入
你将收到以下前置分析结果：
- Step 1: 项目元信息
- Step 2: 代码结构信息
- Step 3: 环境依赖信息
- Step 4: 工具功能详细分析

# 转换重点
1. **语言通俗化**：将技术术语转换为日常用语
2. **功能价值化**：强调工具解决的实际问题
3. **使用场景化**：提供具体的应用场景
4. **操作简单化**：简化使用步骤和说明
5. **错误友好化**：提供易懂的错误处理指导

# 转换原则
## 语言风格
- 使用简单直白的中文表达
- 避免技术术语，用生活化的比喻
- 采用积极正面的语调
- 突出工具的实用价值

## 描述结构
- 先说功能，再说用途
- 先说简单的，再说复杂的
- 先说常用的，再说特殊的
- 先说好处，再说限制

## 示例设计
- 提供真实可用的示例
- 覆盖主要使用场景
- 包含常见参数组合
- 展示预期的输出结果

# 输出格式要求
严格按照以下JSON格式输出：

```json
{
  "step5_result": {
    "user_friendly_tools": [
      {
        "tool_id": "对应Step4的工具ID",
        "names": {
          "name_chinese": "简洁的中文工具名",
          "name_english": "英文函数名",
          "display_name": "用户界面显示名称",
          "keywords": ["搜索关键词列表"]
        },
        "descriptions": {
          "description": "简洁的英文功能描述（面向开发者）",
          "description_chinese": "详细的中文功能说明（面向普通用户）",
          "one_line_summary": "一句话功能总结",
          "value_proposition": "核心价值主张"
        },
        "user_scenarios": {
          "primary_use_cases": [
            {
              "scenario": "使用场景名称",
              "description": "场景详细描述",
              "user_benefit": "用户获得的好处",
              "frequency": "使用频率：daily|weekly|occasional"
            }
          ],
          "target_users": [
            {
              "user_type": "用户类型",
              "skill_level": "技能水平：beginner|intermediate|advanced",
              "motivation": "使用动机",
              "pain_points": ["解决的痛点"]
            }
          ]
        },
        "usage_guide": {
          "quick_start": {
            "steps": ["快速开始步骤"],
            "time_estimate": "预计用时",
            "prerequisites": ["前置条件"]
          },
          "sample_prompts": [
            {
              "prompt": "用户提示词示例",
              "scenario": "适用场景",
              "expected_result": "预期结果",
              "difficulty": "简单|中等|复杂"
            }
          ],
          "parameter_guide": [
            {
              "parameter": "参数名",
              "user_friendly_name": "用户友好的参数名",
              "explanation": "通俗易懂的解释",
              "examples": ["示例值"],
              "tips": ["使用技巧"],
              "common_mistakes": ["常见错误"]
            }
          ]
        },
        "practical_examples": [
          {
            "title": "示例标题",
            "description": "示例描述",
            "input": {
              "user_input": "用户输入示例",
              "parameters": "参数设置"
            },
            "output": {
              "expected_result": "预期输出",
              "explanation": "结果解释"
            },
            "variations": ["变化示例"]
          }
        ],
        "troubleshooting": {
          "common_issues": [
            {
              "problem": "常见问题描述",
              "symptoms": ["问题症状"],
              "causes": ["可能原因"],
              "solutions": ["解决方案"],
              "prevention": "预防措施"
            }
          ],
          "error_messages": [
            {
              "error": "错误信息",
              "meaning": "错误含义",
              "action": "用户应该怎么做"
            }
          ]
        },
        "best_practices": {
          "dos": ["推荐做法"],
          "donts": ["不推荐做法"],
          "optimization_tips": ["优化建议"],
          "advanced_usage": ["高级用法"]
        },
        "user_experience_metrics": {
          "ease_of_use": "易用性评分1-10",
          "learning_curve": "学习难度：easy|moderate|steep",
          "satisfaction_potential": "满意度潜力1-10",
          "accessibility": "可访问性评分1-10"
        }
      }
    ],
    "project_user_guide": {
      "overview": {
        "what_it_does": "项目功能概述（用户语言）",
        "why_useful": "为什么有用",
        "who_should_use": "适合谁使用",
        "when_to_use": "什么时候使用"
      },
      "getting_started": {
        "installation_simplified": ["简化的安装步骤"],
        "first_time_setup": ["首次设置指导"],
        "verification_steps": ["验证步骤"],
        "common_setup_issues": ["常见设置问题"]
      },
      "workflow_examples": [
        {
          "workflow_name": "工作流名称",
          "description": "工作流描述",
          "steps": ["详细步骤"],
          "tools_used": ["使用的工具"],
          "expected_outcome": "预期结果"
        }
      ]
    },
    "content_optimization": {
      "seo_keywords": ["SEO关键词"],
      "search_phrases": ["搜索短语"],
      "category_tags": ["分类标签"],
      "difficulty_tags": ["难度标签"]
    }
  }
}
```

# 转换指导原则
1. **用户中心**：始终从用户角度思考和表达
2. **价值导向**：突出工具带来的实际价值
3. **场景驱动**：基于真实使用场景设计内容
4. **渐进式**：从简单到复杂，循序渐进
5. **可操作性**：确保用户能够按照指导成功使用

# 特殊转换规则
- "API"说成"接口服务"
- "Schema"说成"参数格式"
- "异步处理"说成"后台处理"
- "错误处理"说成"问题解决"
- "配置"说成"设置"

# 质量检查标准
- 描述是否通俗易懂
- 示例是否真实可用
- 指导是否完整清晰
- 错误处理是否友好
- 整体体验是否流畅

现在请基于前面四个步骤的分析结果，开始进行用户友好转换。
```

## 🔧 调用示例

```python
import json
from typing import Dict, List, Any

class Step5Executor:
    def __init__(self, api_key):
        self.client = AsyncOpenAI(api_key=api_key)
        self.system_prompt = """上面的System Prompt内容"""
        
        # 用户友好转换规则
        self.conversion_rules = {
            'technical_terms': {
                'API': '接口服务',
                'Schema': '参数格式',
                'JSON': '数据格式',
                'HTTP': '网络请求',
                'async': '后台处理',
                'callback': '回调函数',
                'endpoint': '服务地址'
            },
            'complexity_levels': {
                'simple': '简单易用',
                'medium': '中等难度',
                'complex': '高级功能'
            }
        }
    
    async def execute(self, context):
        """
        执行Step 5分析
        
        Args:
            context: {
                'step1_result': Step1结果,
                'step2_result': Step2结果,
                'step3_result': Step3结果,
                'step4_result': Step4结果
            }
        
        Returns:
            dict: Step 5的分析结果
        """
        
        # 预处理：提取关键信息
        key_info = self._extract_key_information(context)
        
        # 构建用户输入
        user_input = self._build_user_input(context, key_info)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.3,  # 稍高温度增加创造性
                max_tokens=8000
            )
            
            result = self._parse_response(response.choices[0].message.content)
            return result
            
        except Exception as e:
            return self._get_fallback_result(e)
    
    def _extract_key_information(self, context) -> Dict:
        """提取关键信息用于转换"""
        key_info = {
            'project_name': '',
            'main_purpose': '',
            'tools_summary': [],
            'complexity_level': 'medium',
            'target_audience': []
        }
        
        # 从Step1提取项目信息
        step1 = context.get('step1_result', {})
        project_meta = step1.get('project_metadata', {})
        key_info['project_name'] = project_meta.get('display_name', project_meta.get('name', ''))
        key_info['main_purpose'] = project_meta.get('description_chinese', project_meta.get('description', ''))
        
        # 从Step4提取工具信息
        step4 = context.get('step4_result', {})
        tools_analysis = step4.get('tools_detailed_analysis', [])
        
        for tool in tools_analysis:
            basic_info = tool.get('basic_info', {})
            functionality = tool.get('functionality_analysis', {})
            
            key_info['tools_summary'].append({
                'name': basic_info.get('name_en', ''),
                'purpose': functionality.get('core_purpose', ''),
                'complexity': basic_info.get('complexity_level', 'medium')
            })
        
        return key_info
    
    def _generate_user_scenarios(self, tool_analysis: Dict) -> List[Dict]:
        """生成用户使用场景"""
        scenarios = []
        
        tool_name = tool_analysis.get('basic_info', {}).get('name_en', '')
        core_purpose = tool_analysis.get('functionality_analysis', {}).get('core_purpose', '')
        
        # 基于工具类型生成场景
        if 'translate' in tool_name.lower():
            scenarios = [
                {
                    'scenario': '多语言文档翻译',
                    'description': '将技术文档翻译成多种语言，方便国际团队协作',
                    'user_benefit': '节省人工翻译时间，提高工作效率',
                    'frequency': 'weekly'
                },
                {
                    'scenario': '跨语言沟通',
                    'description': '与外国客户或同事进行文字交流时的实时翻译',
                    'user_benefit': '消除语言障碍，促进有效沟通',
                    'frequency': 'daily'
                }
            ]
        elif 'image' in tool_name.lower():
            scenarios = [
                {
                    'scenario': '图片内容分析',
                    'description': '分析图片中的内容，提取有用信息',
                    'user_benefit': '自动化图片处理，提高工作效率',
                    'frequency': 'daily'
                }
            ]
        else:
            # 通用场景
            scenarios = [
                {
                    'scenario': '日常工作辅助',
                    'description': f'使用{core_purpose}来辅助日常工作',
                    'user_benefit': '提高工作效率和准确性',
                    'frequency': 'weekly'
                }
            ]
        
        return scenarios
    
    def _generate_sample_prompts(self, tool_analysis: Dict) -> List[Dict]:
        """生成示例提示词"""
        prompts = []
        
        tool_name = tool_analysis.get('basic_info', {}).get('name_en', '')
        
        if 'translate' in tool_name.lower():
            prompts = [
                {
                    'prompt': '帮我把这段英文翻译成中文：Hello, how are you today?',
                    'scenario': '基础翻译',
                    'expected_result': '你好，你今天怎么样？',
                    'difficulty': '简单'
                },
                {
                    'prompt': '将这个技术文档的标题翻译成日语：Getting Started with API',
                    'scenario': '技术文档翻译',
                    'expected_result': 'APIの使い方',
                    'difficulty': '中等'
                }
            ]
        else:
            # 通用提示词
            prompts = [
                {
                    'prompt': f'使用{tool_name}处理我的数据',
                    'scenario': '基础使用',
                    'expected_result': '处理后的结果',
                    'difficulty': '简单'
                }
            ]
        
        return prompts
    
    def _build_user_input(self, context, key_info):
        """构建用户输入"""
        input_parts = [
            "# MCP工具用户体验转换分析",
            "",
            "## 项目概览",
            f"项目名称: {key_info['project_name']}",
            f"主要用途: {key_info['main_purpose']}",
            "",
            "## 前置技术分析结果",
            ""
        ]
        
        # 添加各步骤的关键结果
        for step_name, step_result in context.items():
            if step_name.startswith('step') and step_result:
                input_parts.extend([
                    f"### {step_name.upper()}",
                    json.dumps(step_result, indent=2, ensure_ascii=False)[:3000],  # 限制长度
                    ""
                ])
        
        input_parts.extend([
            "## 转换要求",
            "请将以上技术分析结果转换为用户友好的描述和指南，重点关注：",
            "1. 生成通俗易懂的中英文描述",
            "2. 创建实用的使用示例和场景",
            "3. 提供完整的用户指导",
            "4. 设计友好的错误处理说明",
            ""
        ])
        
        return "\n".join(input_parts)
    
    def _get_fallback_result(self, error):
        """错误降级结果"""
        return {
            "step5_result": {
                "user_friendly_tools": [],
                "project_user_guide": {
                    "overview": {
                        "error": str(error)
                    }
                },
                "content_optimization": {
                    "seo_keywords": [],
                    "search_phrases": [],
                    "category_tags": [],
                    "difficulty_tags": []
                }
            }
        }

# 使用示例
async def main():
    executor = Step5Executor("your-api-key")
    
    context = {
        'step1_result': {
            'project_metadata': {
                'name': 'mcp-baidu-translate',
                'display_name': '百度翻译工具',
                'description_chinese': '基于百度翻译API的多语言文本翻译服务'
            }
        },
        'step4_result': {
            'tools_detailed_analysis': [
                {
                    'tool_id': 'translate_text_001',
                    'basic_info': {
                        'name_en': 'translate_text',
                        'complexity_level': 'medium'
                    },
                    'functionality_analysis': {
                        'core_purpose': '调用百度翻译API进行多语言文本翻译'
                    },
                    'input_schema_analysis': {
                        'schema_structure': {
                            'required': ['text', 'to_lang'],
                            'properties': {
                                'text': {
                                    'type': 'string',
                                    'description': '需要翻译的文本内容'
                                }
                            }
                        }
                    }
                }
            ]
        }
    }
    
    result = await executor.execute(context)
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

这是Step 5的完整设计。最后让我设计Step 6吗？

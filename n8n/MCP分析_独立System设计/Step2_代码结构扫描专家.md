# Step 2: 代码结构扫描专家

## 🎯 System Prompt

```markdown
# 角色定义
你是专业的MCP代码结构分析专家，精通各种编程语言的MCP服务器实现模式。你的专长是快速定位和识别MCP项目中的核心组件和工具定义。

# 核心任务
扫描MCP项目的源代码，准确定位MCP服务器组件、工具定义和处理函数，为后续的详细功能分析提供精确的代码定位信息。

# 输入内容
你将收到以下内容：
- 项目源代码文件内容
- 项目目录结构
- 主要文件的代码片段

# 分析重点
1. **MCP SDK识别**：查找MCP相关的导入语句和初始化代码
2. **服务器定位**：找到MCP服务器的主要实现文件
3. **工具定义**：识别工具定义数组和工具配置
4. **处理函数**：定位每个工具对应的处理函数
5. **代码组织**：分析项目的代码组织结构和模块划分

# 扫描模式
## JavaScript/TypeScript项目
- 查找: `@modelcontextprotocol/sdk` 导入
- 定位: `Server` 类实例化
- 识别: `TOOLS` 数组或类似的工具定义
- 查找: `setRequestHandler` 调用和处理函数

## Python项目  
- 查找: `mcp` 相关包导入
- 定位: MCP服务器初始化代码
- 识别: 工具装饰器或工具注册代码
- 查找: 工具处理函数定义

## 其他语言
- 根据语言特性调整扫描策略
- 识别MCP协议实现模式

# 输出格式要求
严格按照以下JSON格式输出：

```json
{
  "step2_result": {
    "code_structure": {
      "mcp_implementation": {
        "main_server_file": "主服务器文件路径",
        "server_class": "服务器类名或实例名",
        "sdk_version": "使用的MCP SDK版本",
        "implementation_pattern": "实现模式描述"
      },
      "tools_definition": {
        "definition_location": {
          "file": "工具定义所在文件",
          "line_range": "行号范围",
          "variable_name": "工具定义变量名"
        },
        "definition_pattern": "array|object|decorator|other",
        "tools_found": [
          {
            "tool_name": "工具名称",
            "definition_line": "定义行号",
            "schema_complete": true
          }
        ]
      },
      "tool_handlers": [
        {
          "tool_name": "工具名称",
          "handler_function": "处理函数名",
          "file_location": "文件路径",
          "line_range": "函数行号范围",
          "handler_type": "async|sync|class_method",
          "complexity_estimate": "simple|medium|complex"
        }
      ],
      "code_organization": {
        "total_files_scanned": "扫描的文件总数",
        "main_logic_files": ["核心逻辑文件列表"],
        "helper_modules": ["辅助模块列表"],
        "config_files": ["配置文件列表"],
        "test_files": ["测试文件列表"]
      },
      "architecture_analysis": {
        "separation_of_concerns": "关注点分离程度评估",
        "code_quality_indicators": ["代码质量指标"],
        "potential_issues": ["潜在问题列表"]
      }
    },
    "scan_summary": {
      "total_tools_found": "发现的工具总数",
      "scan_confidence": "0.0-1.0的扫描置信度",
      "missing_components": ["未找到的预期组件"],
      "unusual_patterns": ["不寻常的实现模式"]
    }
  }
}
```

# 分析指导原则
1. **精确定位**：提供准确的文件路径和行号信息
2. **模式识别**：识别常见的MCP实现模式
3. **完整性检查**：确保所有工具都有对应的处理函数
4. **质量评估**：对代码组织和实现质量进行初步评估
5. **异常标记**：标记不寻常或可能有问题的实现模式

# 特殊处理规则
- 如果工具定义和处理函数分离，要建立正确的对应关系
- 识别动态生成的工具定义
- 处理多文件分布的工具实现
- 标记缺失的错误处理代码

# 扫描优先级
1. 首先扫描主入口文件（index.js、main.py等）
2. 然后扫描src/目录下的核心文件
3. 最后扫描辅助模块和配置文件
4. 跳过node_modules、__pycache__等依赖目录

现在请开始扫描提供的MCP项目代码结构。
```

## 🔧 调用示例

```python
import re
import os
from typing import Dict, List, Any

class Step2Executor:
    def __init__(self, api_key):
        self.client = AsyncOpenAI(api_key=api_key)
        self.system_prompt = """上面的System Prompt内容"""
        
        # 代码扫描模式
        self.scan_patterns = {
            'mcp_imports': [
                r'from\s+@modelcontextprotocol/sdk',
                r'import.*@modelcontextprotocol',
                r'from\s+mcp\s+import',
                r'import\s+mcp'
            ],
            'server_init': [
                r'new\s+Server\s*\(',
                r'Server\s*\(',
                r'create_server\s*\(',
                r'MCPServer\s*\('
            ],
            'tools_definition': [
                r'const\s+TOOLS\s*=',
                r'let\s+tools\s*=',
                r'TOOLS\s*:\s*\[',
                r'@tool\s*\n',
                r'tools\s*=\s*\['
            ],
            'request_handlers': [
                r'setRequestHandler\s*\(',
                r'@app\.tool',
                r'handle_.*\s*\(',
                r'async\s+def\s+handle'
            ]
        }
    
    async def execute(self, project_code):
        """
        执行Step 2分析
        
        Args:
            project_code: {
                'files': {
                    'src/index.ts': '文件内容',
                    'package.json': '配置内容'
                },
                'directory_structure': '目录结构'
            }
        
        Returns:
            dict: Step 2的分析结果
        """
        
        # 预处理：代码扫描
        scan_results = self._scan_code_patterns(project_code['files'])
        
        # 构建用户输入
        user_input = self._build_user_input(project_code, scan_results)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_input}
                ],
                temperature=0.1,
                max_tokens=3000
            )
            
            result = self._parse_response(response.choices[0].message.content)
            return result
            
        except Exception as e:
            return self._get_fallback_result(e)
    
    def _scan_code_patterns(self, files: Dict[str, str]) -> Dict:
        """预扫描代码模式"""
        results = {
            'mcp_files': [],
            'tool_definitions': [],
            'handler_functions': [],
            'import_statements': []
        }
        
        for filepath, content in files.items():
            # 跳过非代码文件
            if not self._is_code_file(filepath):
                continue
                
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                # 扫描MCP导入
                for pattern in self.scan_patterns['mcp_imports']:
                    if re.search(pattern, line):
                        results['import_statements'].append({
                            'file': filepath,
                            'line': i + 1,
                            'content': line.strip()
                        })
                
                # 扫描工具定义
                for pattern in self.scan_patterns['tools_definition']:
                    if re.search(pattern, line):
                        results['tool_definitions'].append({
                            'file': filepath,
                            'line': i + 1,
                            'content': line.strip()
                        })
                
                # 扫描处理函数
                for pattern in self.scan_patterns['request_handlers']:
                    if re.search(pattern, line):
                        results['handler_functions'].append({
                            'file': filepath,
                            'line': i + 1,
                            'content': line.strip()
                        })
        
        return results
    
    def _is_code_file(self, filepath: str) -> bool:
        """判断是否为代码文件"""
        code_extensions = ['.js', '.ts', '.py', '.go', '.rs', '.java']
        return any(filepath.endswith(ext) for ext in code_extensions)
    
    def _build_user_input(self, project_code, scan_results):
        """构建用户输入"""
        input_parts = [
            "# MCP项目代码结构分析",
            "",
            "## 项目目录结构",
            project_code.get('directory_structure', ''),
            "",
            "## 预扫描结果",
            f"发现MCP导入语句: {len(scan_results['import_statements'])}个",
            f"发现工具定义: {len(scan_results['tool_definitions'])}个", 
            f"发现处理函数: {len(scan_results['handler_functions'])}个",
            ""
        ]
        
        # 添加关键代码文件
        for filepath, content in project_code['files'].items():
            if self._is_code_file(filepath) and len(content) < 10000:  # 限制文件大小
                input_parts.extend([
                    f"## {filepath}",
                    "```",
                    content,
                    "```",
                    ""
                ])
        
        return "\n".join(input_parts)
    
    def _get_fallback_result(self, error):
        """错误降级结果"""
        return {
            "step2_result": {
                "code_structure": {
                    "error": str(error)
                },
                "scan_summary": {
                    "total_tools_found": 0,
                    "scan_confidence": 0.0,
                    "missing_components": ["所有组件"],
                    "unusual_patterns": []
                }
            }
        }

# 使用示例
async def main():
    executor = Step2Executor("your-api-key")
    
    project_code = {
        'files': {
            'src/index.ts': '''
import { Server } from "@modelcontextprotocol/sdk/server/index.js";

const TRANSLATE_TEXT_TOOL = {
  name: "translate_text",
  description: "使用百度翻译进行文本翻译"
};

const TOOLS = [TRANSLATE_TEXT_TOOL];

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  // 处理逻辑
});
            '''
        },
        'directory_structure': 'src/\n  index.ts\npackage.json'
    }
    
    result = await executor.execute(project_code)
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

## 📊 预期输出示例

```json
{
  "step2_result": {
    "code_structure": {
      "mcp_implementation": {
        "main_server_file": "src/index.ts",
        "server_class": "Server",
        "sdk_version": "@modelcontextprotocol/sdk",
        "implementation_pattern": "标准MCP服务器实现"
      },
      "tools_definition": {
        "definition_location": {
          "file": "src/index.ts",
          "line_range": "15-20",
          "variable_name": "TOOLS"
        },
        "definition_pattern": "array",
        "tools_found": [
          {
            "tool_name": "translate_text",
            "definition_line": 15,
            "schema_complete": true
          }
        ]
      },
      "tool_handlers": [
        {
          "tool_name": "translate_text",
          "handler_function": "CallToolRequestSchema handler",
          "file_location": "src/index.ts",
          "line_range": "22-30",
          "handler_type": "async",
          "complexity_estimate": "medium"
        }
      ]
    },
    "scan_summary": {
      "total_tools_found": 1,
      "scan_confidence": 0.95,
      "missing_components": [],
      "unusual_patterns": []
    }
  }
}
```

这是Step 2的完整设计。接下来我继续设计Step 3吗？
